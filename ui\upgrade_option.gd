extends Button
class_name UpgradeOption

## 升级选项UI组件
##
## 用于显示升级选项的按钮，支持：
## - 显示选项图标和名称
## - 选中状态切换
## - 点击交互
## - 视觉反馈

# 信号定义
signal option_selected(option_data: Dictionary, option_index: int)
# UI节点引用
@onready var icon_label: Label = $VBoxContainer/IconLabel
@onready var name_label: Label = $VBoxContainer/NameLabel

# 数据存储
var option_data: Dictionary = {}
var option_index: int = -1
var is_selected: bool = false
# 样式常量
const NORMAL_COLOR = Color.WHITE
const SELECTED_COLOR = Color(1.2, 1.2, 0.8)


func _ready() -> void:
	# 连接按钮信号
	pressed.connect(_on_option_pressed)

	# 设置初始状态
	modulate = NORMAL_COLOR

	# 检查节点引用
	if not icon_label:
		print("错误：找不到IconLabel节点")
	if not name_label:
		print("错误：找不到NameLabel节点")


## 设置选项数据
## @param data: 选项数据字典，包含name、icon、description等信息
## @param index: 选项索引
func set_option_data(data: Dictionary, index: int) -> void:
	option_data = data
	option_index = index

	# 更新显示
	var option_icon = data.get("icon", "⭐")
	var option_name = data.get("name", "未知选项")

	# 确保节点已经准备好
	if icon_label:
		icon_label.text = option_icon
	else:
		print("警告：IconLabel节点未准备好")

	if name_label:
		name_label.text = option_name
	else:
		print("警告：NameLabel节点未准备好")

	print("设置升级选项数据：", option_name, "，索引：", index)


## 设置选中状态
## @param selected: 是否选中
func set_selected(selected: bool) -> void:
	is_selected = selected

	if selected:
		modulate = SELECTED_COLOR
		print("升级选项被选中：", option_data.get("name", "未知选项"))
	else:
		modulate = NORMAL_COLOR


## 获取选项数据
func get_option_data() -> Dictionary:
	return option_data


## 获取选项索引
func get_option_index() -> int:
	return option_index


## 检查是否被选中
func is_option_selected() -> bool:
	return is_selected


## 选项点击处理
func _on_option_pressed() -> void:

	print("升级选项被点击：", option_data.get("name", "未知选项"))

	# 发射信号，传递选项数据和索引
	option_selected.emit(option_data, option_index)

