[gd_scene load_steps=16 format=3 uid="uid://cvyjbq3bu0mf3"]

[ext_resource type="Script" uid="uid://ubth7cag73ce" path="res://ui/bar.gd" id="1_lruyu"]
[ext_resource type="Shader" uid="uid://c4ijmxnenptwl" path="res://assets/materials/shader/fluid.tres" id="2_3wbqj"]
[ext_resource type="Texture2D" uid="uid://u5ndsxu34wr3" path="res://assets/imgs/balls/128x128.png" id="3_kw0ho"]
[ext_resource type="Texture2D" uid="uid://c46f658slia78" path="res://assets/imgs/effects/34.png" id="4_0x447"]

[sub_resource type="StyleBoxFlat" id="StyleBoxFlat_lruyu"]
bg_color = Color(0, 0, 0, 1)
expand_margin_left = 1.0
expand_margin_top = 1.0
expand_margin_right = 1.0
expand_margin_bottom = 1.0
shadow_color = Color(0, 0, 0, 0.468)
shadow_size = 5

[sub_resource type="StyleBoxFlat" id="StyleBoxFlat_3wbqj"]
bg_color = Color(0.552956, 0.552956, 0.552956, 1)

[sub_resource type="FastNoiseLite" id="FastNoiseLite_lruyu"]
noise_type = 4
frequency = 0.0196
fractal_type = 0

[sub_resource type="NoiseTexture2D" id="NoiseTexture2D_3wbqj"]
resource_local_to_scene = true
seamless = true
seamless_blend_skirt = 1.0
noise = SubResource("FastNoiseLite_lruyu")

[sub_resource type="FastNoiseLite" id="FastNoiseLite_kw0ho"]
noise_type = 4
frequency = 0.02
fractal_lacunarity = 0.135

[sub_resource type="NoiseTexture2D" id="NoiseTexture2D_agqpj"]
resource_local_to_scene = true
width = 852
seamless = true
seamless_blend_skirt = 0.681
noise = SubResource("FastNoiseLite_kw0ho")

[sub_resource type="ShaderMaterial" id="ShaderMaterial_lruyu"]
resource_local_to_scene = true
shader = ExtResource("2_3wbqj")
shader_parameter/BaseColor = Color(0, 0.556412, 0.951984, 1)
shader_parameter/BaseColor2 = Color(0.05227, 0, 0.621127, 1)
shader_parameter/WaveNoise = SubResource("NoiseTexture2D_3wbqj")
shader_parameter/OffsetSpeed = 0.025
shader_parameter/OffsetDir = Vector2(-1, 0)
shader_parameter/offset = SubResource("NoiseTexture2D_agqpj")
shader_parameter/EdgeWaveTense = 5.0
shader_parameter/EdgeWaveSpeed = 0.5
shader_parameter/EdgeWaveScale = 0.5
shader_parameter/EdgeWaveLevel = 50.0

[sub_resource type="CanvasItemMaterial" id="CanvasItemMaterial_sfenn"]
blend_mode = 1

[sub_resource type="Curve" id="Curve_8jtfe"]
_data = [Vector2(0, 1), 0.0, -0.15433, 0, 0, Vector2(1, 0), -3.24463, 0.0, 0, 0]
point_count = 2

[sub_resource type="CurveTexture" id="CurveTexture_4d2ui"]
curve = SubResource("Curve_8jtfe")

[sub_resource type="ParticleProcessMaterial" id="ParticleProcessMaterial_obita"]
lifetime_randomness = 0.5
particle_flag_disable_z = true
emission_shape_offset = Vector3(4, 13.5, 0)
emission_shape_scale = Vector3(5, 1, 1)
emission_shape = 3
emission_box_extents = Vector3(1, 13, 1)
direction = Vector3(0, 0, 0)
spread = 0.0
initial_velocity_max = 10.0
gravity = Vector3(0, 0, 0)
linear_accel_min = 30.0
linear_accel_max = 35.0
scale_min = 0.01
scale_max = 0.02
color = Color(0, 0.494118, 1, 0.784314)
alpha_curve = SubResource("CurveTexture_4d2ui")

[node name="Exp" type="Control"]
layout_mode = 3
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
grow_horizontal = 2
grow_vertical = 2
script = ExtResource("1_lruyu")

[node name="Bottom" type="ProgressBar" parent="."]
custom_minimum_size = Vector2(240, 0)
layout_mode = 0
offset_right = 240.0
offset_bottom = 27.0
theme_override_styles/background = SubResource("StyleBoxFlat_lruyu")
theme_override_styles/fill = SubResource("StyleBoxFlat_3wbqj")
show_percentage = false

[node name="Top" type="TextureProgressBar" parent="."]
texture_repeat = 1
material = SubResource("ShaderMaterial_lruyu")
custom_minimum_size = Vector2(240, 0)
layout_mode = 0
offset_right = 240.0
offset_bottom = 27.0
mouse_filter = 0
step = 0.01
value = 50.0
nine_patch_stretch = true
stretch_margin_left = 1
stretch_margin_top = 1
stretch_margin_right = 1
stretch_margin_bottom = 1
texture_progress = ExtResource("3_kw0ho")

[node name="GPUParticles2D" type="GPUParticles2D" parent="."]
material = SubResource("CanvasItemMaterial_sfenn")
amount = 500
texture = ExtResource("4_0x447")
lifetime = 3.0
preprocess = 3.0
process_material = SubResource("ParticleProcessMaterial_obita")
