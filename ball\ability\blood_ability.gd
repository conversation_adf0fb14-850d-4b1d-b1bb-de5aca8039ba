class_name BloodAbility
extends BallAbilityBase

@export var blood_debuff_template: AttributeBuffDOT

# 当弹球命中敌人时触发
func on_primary_hit(source: BallBase, target: EnemyBase) -> void:
	if blood_debuff_template == null:
		push_warning("blood_debuff_template is not set in BloodAbility")
		return
	
	if target.has_method("add_buff"):
		target.add_buff(blood_debuff_template)

func on_secondary_hit(source: BallBase, target: EnemyBase, source_ability: BallAbilityBase) -> void:
	if blood_debuff_template == null:
		push_warning("blood_debuff_template is not set in BloodAbility")
		return
	
	if target.has_method("add_buff"):
		target.add_buff(blood_debuff_template)