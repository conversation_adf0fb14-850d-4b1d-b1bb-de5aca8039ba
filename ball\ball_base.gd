class_name BallBase
extends RigidBody2D

@onready var abilities: Node = $Abilities
@onready var attribute_component: AttributeComponent = $AttributeComponent
@onready var sprite: Sprite2D = $Sprite2D

@export var cooldown: float = 10.0
@export var acceleration_multiplier: float = 1.0
@export var damage_color: Color = Color.WHITE

var speed: float:
	get:
		return attribute_component.get_attribute_value("speed")
	set(value):
		attribute_component.set_attribute_value("speed", value)

var min_damage:
	get:
		return attribute_component.get_attribute_value("min_damage")

var max_damage:
	get:
		return attribute_component.get_attribute_value("max_damage")

var level:
	get:
		return attribute_component.get_attribute_value("level")
	set(new_level):
		attribute_component.set_attribute_value("level", new_level)
		for ability in get_abilities():
			ability.level = new_level

var is_active: bool = false
var cooldown_timer: float = 0.0
var is_cooldown_finished: bool = false
var is_accelerating: bool = false
# 追踪回收相关变量
var is_tracking: bool = false
var tracking_target: Node2D = null
var tracking_speed: float = 300.0
var tracking_lag_factor: float = 0.8 # 转向迟滞系数，越小越迟滞
var tracking_arrival_distance: float = 50.0 # 到达距离阈值
var _tracking_direction: Vector2 = Vector2.ZERO
var _original_collision_mask: int = 0 # 保存原始碰撞遮罩
var _was_colliding: bool = false
var _collision_count: int = 0
var _is_first_frame := false
var _initial_direction := Vector2.ZERO
var visual_bounds: Rect2
signal reach_bottom(ball)
signal tracking_arrived(ball) # 追踪到达信号


func _ready():
	# 开启碰撞监控
	visual_bounds = get_viewport().get_visible_rect()
	contact_monitor = true
	max_contacts_reported = 1
	body_entered.connect(_on_body_entered)


func _on_body_entered(body: Node):
	if body is EnemyBase:
		# emit event first so relics can modify ball before damage calculation
		var collision_info := {
			"enemy_pos": body.global_position,
			"ball_pos": self.global_position
		}
		GameEvents.ball_hit_enemy.emit(self, body, collision_info)
		var damage_info: Dictionary = get_damage()
		body.take_damage(damage_info["damage"], damage_color, self, false, damage_info["is_crit"])
		# 遍历弹球所有特性组件，逐一处理
		if has_method("process_primary_hit"):
			process_primary_hit(body)


func get_damage() -> Dictionary:
	var dmg_attr: Attribute = attribute_component.find_attribute("damage")
	if dmg_attr == null:
		return {
			"damage": 0,
			"is_crit": false
		}
	var dmg: float = dmg_attr.get_value(true, true)
	var is_crit: bool = false
	if dmg_attr is BallDamageAttribute:
		is_crit = (dmg_attr as BallDamageAttribute).pop_last_crit()
	return {
		"damage": dmg,
		"is_crit": is_crit
	}


func _process(delta) -> void:
	if not is_active:
		return

	# 处理追踪逻辑
	if is_tracking:
		update_tracking(delta)
		return

	update_cooldown(delta)

	for ability in get_abilities():
		ability.on_physics_update(delta)


# 初始化弹球
func init(direction: Vector2):
	process_mode = PhysicsBody2D.PROCESS_MODE_INHERIT
	is_active = true
	cooldown_timer = 0.0
	is_cooldown_finished = false
	is_accelerating = false
	is_tracking = false
	tracking_target = null
	_tracking_direction = Vector2.ZERO
	_was_colliding = false
	_collision_count = 0
	_is_first_frame = true
	_initial_direction = direction

	# 恢复原始碰撞设置
	enable()

	self.gravity_scale = 0
	var trail = $Sprite2D/Trail if has_node("Sprite2D/Trail") else null
	if trail and trail.has_method("clear_points"):
		trail.clear_points()


func update_cooldown(delta) -> void:
	if is_cooldown_finished:
		return
	cooldown_timer += delta
	if cooldown_timer >= cooldown:
		is_cooldown_finished = true
		start_acceleration()


func start_acceleration() -> void:
	if is_accelerating:
		return
	is_accelerating = true
	self.gravity_scale = acceleration_multiplier
	print("弹球冷却结束，开始加速回收，类型: %s" % str(get_instance_id()))


# 获取所有能力组件
func get_abilities() -> Array:
	return abilities.get_children() if abilities else []


# 当弹球物理碰撞敌人时，由 EnemyBase 调用此函数作为入口
func process_primary_hit(target: EnemyBase):
	# 遍历所有能力，触发主目标命中事件
	for ability in get_abilities():
		if ability.has_method("on_primary_hit"):
			ability.on_primary_hit(self, target)


# 用于传播效果到次要目标
func process_secondary_hit(target: EnemyBase, source_ability: BallAbilityBase):
	# 遍历所有能力，触发次要目标命中事件
	for ability in get_abilities():
		# 确保不会再次触发源能力自己，避免无限传播
		if ability == source_ability:
			continue

		if ability.has_method("on_secondary_hit"):
			ability.on_secondary_hit(self, target, source_ability)


# 到达底端事件，可重写
func on_reach_bottom():
	reach_bottom.emit(self)


# 获取冷却进度（0-1）
func get_cooldown_progress() -> float:
	if is_cooldown_finished:
		return 1.0
	return min(cooldown_timer / cooldown, 1.0)


# 检查冷却是否结束
func is_cooldown_complete() -> bool:
	return is_cooldown_finished


func _integrate_forces(state: PhysicsDirectBodyState2D) -> void:
	if not is_active:
		return
	if not visual_bounds.has_point(global_position):
		BallPoolManager.recycle_ball(self)
		return
	# 如果处于追踪状态，跳过物理计算
	if is_tracking:
		return

	if _is_first_frame:
		state.linear_velocity = _initial_direction.normalized() * speed
		_is_first_frame = false

	var is_colliding_now = state.get_contact_count() > 0
	var is_new_collision: bool = is_colliding_now and not _was_colliding
	_was_colliding = is_colliding_now

	var target_velocity: Vector2
	var current_velocity: Vector2 = state.linear_velocity

	if not is_accelerating:
		# 非加速状态：目标速度大小恒定
		target_velocity = current_velocity.normalized() * speed
	else:
		# 加速状态：允许重力影响速度，仅在新碰撞时增加速度
		target_velocity = current_velocity
		if is_new_collision:
			_collision_count += 1
			target_velocity = current_velocity.normalized() * speed * min(pow(1.03, _collision_count), 2)

	state.linear_velocity = target_velocity


# 禁用与敌人的碰撞检测，开始追踪模式
func disable():
	await get_tree().physics_frame
	_original_collision_mask = collision_mask

	set_collision_mask_value(4, false)
	set_collision_mask_value(3, false)
	set_collision_mask_value(1, false)

	# 停止物理模拟，改为手动控制
	freeze = true


# 恢复原始碰撞检测设置
func enable():
	if _original_collision_mask != 0:
		collision_mask = _original_collision_mask
	else:
		# 默认碰撞设置：与层1、3、4碰撞
		collision_mask = 13

	# 恢复物理模拟
	freeze = false


# 开始追踪目标
func start_tracking(target: Node2D) -> void:
	if not is_instance_valid(target):
		return

	tracking_target = target
	is_tracking = true
	_tracking_direction = (target.global_position - global_position).normalized()
	tracking_speed = self.linear_velocity.length() * 1.2

	# 禁用碰撞检测
	disable()


# 更新追踪逻辑
func update_tracking(delta: float) -> void:
	if not is_instance_valid(tracking_target):
		stop_tracking()
		return

	# 计算到目标的方向
	var target_direction = (tracking_target.global_position - global_position).normalized()

	# 使用迟滞因子平滑转向
	_tracking_direction = _tracking_direction.lerp(target_direction, tracking_lag_factor)
	_tracking_direction = _tracking_direction.normalized()

	#tracking_speed逐渐变快
	tracking_speed = lerp(tracking_speed, 1000.0, 0.05 * delta)

	# 移动弹球
	var movement = _tracking_direction * tracking_speed * delta
	global_position += movement

	# 检查是否到达目标
	var distance_to_target = global_position.distance_to(tracking_target.global_position)
	if distance_to_target <= tracking_arrival_distance:
		tracking_arrived.emit(self)


# 停止追踪
func stop_tracking():
	is_tracking = false
	tracking_target = null
	_tracking_direction = Vector2.ZERO


func get_rect() -> Rect2:
	return sprite.get_global_transform() * sprite.get_rect()
