extends Node2D

@export_category("Aim Line")
@export var aim_line_length: float = 600.0
@export var aim_line_max_reflect: int = 1
@export var aim_line_dash_length: float = 20.0
@export var aim_line_gap_length: float = 10.0
@export var aim_line_width: float = 2.0
@export var aim_line_color: Color = Color(1, 1, 1)
var aim_line_dash_offset: float = 0.0

var current_dir: Vector2 = Vector2.DOWN


func _process(delta: float):
	# 驱动瞄准线虚线动画并请求重绘
	aim_line_dash_offset += delta * 100
	queue_redraw()


func update_aim(target_pos: Vector2):
	var raw_dir = global_position - target_pos
	var angle = get_limited_angle(raw_dir)
	rotation_degrees = angle - 90
	var rad = deg_to_rad(angle)
	current_dir = -Vector2(cos(rad), sin(rad)).normalized()


func get_current_dir() -> Vector2:
	return current_dir


func get_limited_angle(raw_dir: Vector2) -> float:
	var angle = rad_to_deg(atan2(raw_dir.y, raw_dir.x))
	if angle <= 10 and angle > -90:
		angle = 10
	if angle >= 170 or angle <= -90:
		angle = 170
	return angle


func _draw():
	var player: Player<PERSON>anager = get_parent()
	if not player:
		return
		
	var points = get_aim_line_points(global_position, current_dir, aim_line_length, aim_line_max_reflect)
	for i in range(points.size() - 1):
		draw_custom_dashed_line(points[i], points[i + 1], aim_line_color, aim_line_width, aim_line_dash_length, aim_line_gap_length, aim_line_dash_offset)


func get_aim_line_points(start_pos: Vector2, direction: Vector2, max_length: float, max_reflect: int) -> Array:
	var player: PlayerManager = get_parent()
	if not player:
		return [start_pos]
		
	var points = [start_pos]
	var current_pos = start_pos
	var current_dir = direction.normalized()
	var remain_length = max_length
	var reflect_count = 0

	while reflect_count <= max_reflect and remain_length > 0:
		var to_pos = current_pos + current_dir * remain_length
		var space_state = get_world_2d().direct_space_state
		var query = PhysicsRayQueryParameters2D.create(current_pos, to_pos)
		query.exclude = [player, self] + player.active_balls
		var result = space_state.intersect_ray(query)
		if result:
			var hit_pos = result.position
			points.append(hit_pos)
			remain_length -= current_pos.distance_to(hit_pos)
			# 计算反射方向
			var normal = result.normal
			current_dir = current_dir.bounce(normal)
			current_pos = hit_pos + normal * 0.1
			reflect_count += 1
		else:
			points.append(to_pos)
			break
	return points


func draw_custom_dashed_line(from: Vector2, to: Vector2, color: Color, width: float, dash_len: float, gap_len: float, offset: float):
	var total_len = from.distance_to(to)
	var dir = (to - from).normalized()
	var cycle = dash_len + gap_len
	var progress = fposmod(offset, cycle)
	var pos = from + dir * progress
	var remain = total_len - progress
	while remain > 0:
		var seg_len = min(dash_len, remain)
		var seg_end = pos + dir * seg_len
		draw_line(to_local(pos), to_local(seg_end), color, width)
		pos = seg_end + dir * gap_len
		remain -= (dash_len + gap_len)
