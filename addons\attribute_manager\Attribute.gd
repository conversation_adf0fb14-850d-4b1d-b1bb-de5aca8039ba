@tool
class_name Attribute
extends Resource

signal attribute_changed(attribute: Attribute)
signal buff_added(attribute: Attribute, buff: AttributeBuff)
signal buff_removed(attribute: Attribute, buff: AttributeBuff)
## 属性名称
var attribute_name: StringName

## 属性的原始数值（保持不变）
@export var base_value := 0.0: set = setter_base_value
@export var can_cache := true

## 仅执行计算公式后的数值
var computed_value := 0.0: set = setter_computed_value
## 缓存系统
var _cached_value := 0.0
var is_dirty := true
var is_calculating := false
var derived_from_attributes_fetched := false
var derived_from_attributes: Array[Attribute] = []
## 防止base_value可写
var is_initialized_base_value: bool = false
## 储存对属性值产生影响Buff的缓存
var buffs: Array[AttributeBuff] = []
## 该属性位于的属性集
var _attribute_set_ref: WeakRef

var attribute_set:
	get:
		if _attribute_set_ref:
			return _attribute_set_ref.get_ref()
		return null
	set(value):
		if value is WeakRef:
			_attribute_set_ref = value
		elif value is Object:
			_attribute_set_ref = weakref(value)
		else:
			_attribute_set_ref = null


static func create(value: float = 0.0) -> Attribute:
	var attribute = Attribute.new()
	attribute.base_value = value
	return attribute


#region setter
func setter_base_value(v) -> void:
	# 在编辑器内始终允许改动
	if Engine.is_editor_hint():
		base_value = v
		return

	## 在export设置完成一次后，不再可写
	if not is_initialized_base_value:
		is_initialized_base_value = true
		base_value = v
		computed_value = v


func setter_computed_value(v):
	computed_value = v
	_mark_dirty()


func setter_is_dirty(v):
	is_dirty = v
	## 通知下游的关联属性，更新数值
	if is_dirty:
		attribute_changed.emit(self)


#endregion


#region 外部函数
func notify_attribute_changed():
	setter_is_dirty(true)


func update_computed_value():
	_mark_dirty()


## 由外部驱动（AttributeSet）
func run_process(delta: float):
	var pending_remove_buffs: Array[AttributeBuff] = []

	## 准备删除
	for _buff in buffs:
		_buff.run_process(delta)

		if _buff.is_pending_remove:
			pending_remove_buffs.append(_buff)

	## 确认删除
	for _buff in pending_remove_buffs:
		remove_buff(_buff)


func get_base_value() -> float:
	return base_value


func get_value(consume_buffs: bool = false, consume_buffs_dependencies: bool = false) -> float:
	if not can_cache and not consume_buffs_dependencies:
		update_computed_value()
		computed_value = _compute_value(computed_value)

	if can_cache and not is_dirty and not consume_buffs and not consume_buffs_dependencies: # 如果不消耗buff，且缓存有效，则直接返回
		return _cached_value

	if is_calculating:
		push_warning("Circular dependency detected in attribute calculation for %s" % attribute_name)
		return computed_value # 返回一个较为安全的值，避免无限递归

	is_calculating = true

	var attribute_value: float = computed_value
	if consume_buffs_dependencies:
		attribute_value = _get_derived_from_value(computed_value, true)

	for _buff in buffs:
		# 如果需要消耗，并且当前是次数类Buff，则消耗它
		if consume_buffs and _buff is AttributeBuffCount:
			var executed: bool = (_buff as AttributeBuffCount).consume()
			if executed:
				attribute_value = _buff.operate(attribute_value)
		elif _buff is AttributeBuffDOT:
			continue
		else:
			attribute_value = _buff.operate(attribute_value)

	var final_value: float = post_attribute_value_changed(attribute_value)

	# 只有在不消耗buff的情况下才更新缓存
	if can_cache and not consume_buffs and not consume_buffs_dependencies:
		_cached_value = final_value
		is_dirty = false

	is_calculating = false

	return final_value


func set_value(_value: float):
	var operated_value: float = AttributeModifier.forcefully_set_value(_value).operate(computed_value)
	computed_value = _compute_value(operated_value)


func add(_value: float):
	var operated_value: float = AttributeModifier.add(_value).operate(computed_value)
	computed_value = _compute_value(operated_value)


func sub(_value: float):
	var operated_value: float = AttributeModifier.subtract(_value).operate(computed_value)
	computed_value = _compute_value(operated_value)


func mult(_value: float):
	var operated_value: float = AttributeModifier.multiply(_value).operate(computed_value)
	computed_value = _compute_value(operated_value)


func div(_value: float):
	var operated_value: float = AttributeModifier.divide(_value).operate(computed_value)
	computed_value = _compute_value(operated_value)


func get_buff_size() -> int:
	return buffs.size()


func apply_buff_operation(_buff: AttributeBuff):
	if is_instance_valid(_buff):
		computed_value = post_attribute_value_changed(_buff.operate(computed_value))
		_mark_dirty()


## @ return: 返回duplicated之后的buff引用，remove_buff需传入此引用。
func add_buff(_buff: AttributeBuff) -> AttributeBuff:
	if not is_instance_valid(_buff):
		return null

	var should_append_buff: bool = true
	var pending_add_buff: AttributeBuff = _buff

	## 有命名的Buff时，处理重复Buff的duration逻辑
	if not _buff.buff_name.is_empty():
		var existing_buff: AttributeBuff = find_buff(_buff.buff_name)
		if is_instance_valid(existing_buff):
			should_append_buff = false
			pending_add_buff = existing_buff

			## max_stacks > 1 或 == 0 为可叠加buff
			var is_stackable: bool = existing_buff.max_stacks > 1 or existing_buff.max_stacks == 0
			if is_stackable:
				if existing_buff.max_stacks == 0 or existing_buff.current_stacks < existing_buff.max_stacks:
					existing_buff.current_stacks += 1

			match existing_buff.merging:
				AttributeBuff.DurationMerging.Restart: existing_buff.restart_duration()
				AttributeBuff.DurationMerging.Addtion: existing_buff.extend_duration(existing_buff.duration)
				AttributeBuff.DurationMerging.NoEffect: pass

	if should_append_buff:
		var duplicated_buff: AttributeBuff = _buff.duplicate_buff()
		duplicated_buff.applied_attribute = weakref(self)

		buffs.append(duplicated_buff)
		pending_add_buff = duplicated_buff

	buff_added.emit(self, pending_add_buff)
	_mark_dirty()
	return pending_add_buff


func remove_buff(_buff: AttributeBuff) -> void:
	if not is_instance_valid(_buff):
		return

	buffs.erase(_buff)
	buff_removed.emit(self, _buff)
	_mark_dirty()


func find_buff(buff_name: String) -> AttributeBuff:
	for _buff in buffs:
		if _buff.buff_name == buff_name:
			return _buff
	return null


#endregion

#region 子类继承实现
## 自定义计算公式
## @ operated_value: 已经被修改过后的值
## @ _compute_params: 参数列表中的属性顺序和_derived_from返回的一致
func custom_compute(operated_value: float, _compute_params: Array[Attribute]) -> float:
	return operated_value


## 属性依赖列表
## @ return: 返回依赖属性的名称数组
func derived_from() -> Array[String]:
	return []


## 属性数值最后发生改变的事件，即完成公式计算和Buff叠加后的数值。
## 可以在此做一些clamp操作
func post_attribute_value_changed(_value: float) -> float:
	return _value


#endregion

#region 内部函数
func _mark_dirty() -> void:
	if is_dirty:
		return
	setter_is_dirty(true)


func _get_derived_from_value(operated_value: float, consume_buffs: bool) -> float:
	if not derived_from_attributes_fetched:
		var derived_attribute_names: Array[String] = derived_from()
		for _name in derived_attribute_names:
			var attribute = attribute_set.find_attribute(_name)
			derived_from_attributes.append(attribute)
		derived_from_attributes_fetched = true

	var derived_params_for_compute: Array[Attribute] = []
	for attr in derived_from_attributes:
		derived_params_for_compute.append(Attribute.create(attr.get_value(consume_buffs, consume_buffs)))

	return custom_compute(operated_value, derived_params_for_compute)


## 由计算公式返回数值结果(custom_compute)
func _compute_value(_operated_value: float) -> float:
	var derived_attributs: Array[Attribute] = []
	var derived_attribute_names: Array[String] = derived_from()
	for _name in derived_attribute_names:
		var attribute = attribute_set.find_attribute(_name)
		derived_attributs.append(attribute)
	return custom_compute(_operated_value, derived_attributs)
	#endregion
