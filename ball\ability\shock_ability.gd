class_name ShockAbility
extends Ball<PERSON>bilityBase

@onready var attribute_component: AttributeComponent = $AttributeComponent

var level: int:
	get:
		return attribute_component.get_attribute_value("level") as int
	set(new_level):
		attribute_component.set_attribute_value("level", new_level)

var damage: float:
	get:
		var min_damage = attribute_component.get_attribute_value("min_damage")
		var max_damage = attribute_component.get_attribute_value("max_damage")
		return randi_range(min_damage, max_damage)



# 当弹球直接命中一个敌人时调用 (主目标命中)。
func on_primary_hit(source: BallBase, target: EnemyBase) -> void:
	call_deferred("_execute_shock_effect", source, target)


func _execute_shock_effect(source: BallBase, target: EnemyBase) -> void:
	# 假设 get_range_enemies() 返回范围内的敌人数组
	var shock_effect_node: Shock = ParticlesPoolManager.get_particle(ParticlesPoolManager.ParticleType.SHOCK, target.global_position)
	var enemies_in_range = await shock_effect_node.get_range_enemies()
	# 请求弹球将其他能力效果传播给这些敌人
	for enemy in enemies_in_range:
		enemy.take_damage(damage, Color.WHITE, self)
		# 主目标已经在 process_primary_hit 中处理过所有能力了，这里跳过
		if enemy == target:
			continue

		# 请求 ball 对这个次要目标触发 secondary_hit 事件
		source.process_secondary_hit(enemy, self)


# 震荡能力本身在次要命中时没有特殊效果，所以此函数为空
func on_secondary_hit(source: BallBase, target: EnemyBase, source_ability: BallAbilityBase) -> void:
	pass
